@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

h2 {
  font-size: 1.875rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Loading States */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 1.5rem;
  width: 1.5rem;
  border: 2px solid transparent;
  border-bottom: 2px solid #2563eb;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Design Helpers */
@media (max-width: 640px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  /* Ensure proper mobile layout */
  body {
    overflow-x: hidden;
  }

  /* Fix mobile navigation issues */
  .mobile-nav-fix {
    position: relative;
    z-index: 60;
  }
}

/* Prevent horizontal scroll on all screen sizes */
html, body {
  overflow-x: hidden;
  width: 100%;
}

/* Fix for mobile viewport issues */
@media (max-width: 1024px) {
  .mobile-content {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Ensure proper spacing on mobile */
  .mobile-padding-fix {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Fix grid layouts on mobile */
  .mobile-grid-fix {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Additional mobile fixes */
@media (max-width: 768px) {
  /* Ensure cards don't overflow */
  .card-mobile-fix {
    max-width: calc(100vw - 2rem);
    overflow: hidden;
  }

  /* Fix text overflow */
  .text-mobile-fix {
    word-break: break-word;
    overflow-wrap: break-word;
  }
}

/* CSS Custom Properties for Layout */
:root {
  --sidebar-width: 256px;
  --sidebar-collapsed-width: 64px;
  --sidebar-border-width: 1px;
  --content-offset: calc(var(--sidebar-width) + var(--sidebar-border-width));
  --content-offset-collapsed: calc(var(--sidebar-collapsed-width) + var(--sidebar-border-width));
}

/* High Specificity Layout Classes - Override Tailwind */
.dashboard-layout .sidebar-content-alignment {
  margin-left: var(--content-offset) !important;
  transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

.dashboard-layout .sidebar-content-alignment-collapsed {
  margin-left: var(--content-offset-collapsed) !important;
  transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

/* Desktop Layout - Force Override */
@media (min-width: 1024px) {
  .dashboard-layout .sidebar-content-alignment {
    margin-left: 257px !important; /* Exact pixel value */
    transform: translateZ(0);
  }

  .dashboard-layout .sidebar-content-alignment-collapsed {
    margin-left: 65px !important; /* Exact pixel value */
    transform: translateZ(0);
  }
}

/* Mobile/Tablet - Reset margins */
@media (max-width: 1023px) {
  .dashboard-layout .sidebar-content-alignment,
  .dashboard-layout .sidebar-content-alignment-collapsed {
    margin-left: 0 !important;
  }
}

/* Navigation Sidebar Exact Dimensions */
.navigation-sidebar {
  width: var(--sidebar-width) !important;
  border-right: var(--sidebar-border-width) solid #e5e7eb !important;
}

.navigation-sidebar.collapsed {
  width: var(--sidebar-collapsed-width) !important;
}

/* Ensure header spans full width */
.dashboard-layout .sidebar-content-alignment header,
.dashboard-layout .sidebar-content-alignment-collapsed header {
  width: 100% !important;
  position: relative !important;
}

/* Prevent sub-pixel rendering issues */
.dashboard-layout .sidebar-content-alignment,
.dashboard-layout .sidebar-content-alignment-collapsed {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Ultimate Layout Fix - Maximum Specificity */
html body .dashboard-layout .sidebar-content-alignment {
  margin-left: 257px !important;
  padding-left: 0 !important;
  left: 0 !important;
  right: auto !important;
  position: relative !important;
  box-sizing: border-box !important;
}

html body .dashboard-layout .sidebar-content-alignment-collapsed {
  margin-left: 65px !important;
  padding-left: 0 !important;
  left: 0 !important;
  right: auto !important;
  position: relative !important;
  box-sizing: border-box !important;
}

/* Mobile Override */
@media (max-width: 1023px) {
  html body .dashboard-layout .sidebar-content-alignment,
  html body .dashboard-layout .sidebar-content-alignment-collapsed {
    margin-left: 0 !important;
  }
}

/* Debug mode for layout alignment (remove in production) */
.debug-layout {
  position: relative;
}

.debug-layout::before {
  content: '';
  position: fixed;
  top: 0;
  left: 257px; /* Sidebar width + border */
  width: 2px;
  height: 100vh;
  background: rgba(255, 0, 0, 0.8);
  z-index: 9999;
  pointer-events: none;
}

.debug-layout.collapsed::before {
  left: 65px; /* Collapsed sidebar width + border */
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  .debug-layout::before {
    display: none;
  }
}
