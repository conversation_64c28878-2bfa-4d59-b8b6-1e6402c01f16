@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

h2 {
  font-size: 1.875rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Loading States */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 1.5rem;
  width: 1.5rem;
  border: 2px solid transparent;
  border-bottom: 2px solid #2563eb;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Design Helpers */
@media (max-width: 640px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  /* Ensure proper mobile layout */
  body {
    overflow-x: hidden;
  }

  /* Fix mobile navigation issues */
  .mobile-nav-fix {
    position: relative;
    z-index: 60;
  }
}

/* Prevent horizontal scroll on all screen sizes */
html, body {
  overflow-x: hidden;
  width: 100%;
}

/* Fix for mobile viewport issues */
@media (max-width: 1024px) {
  .mobile-content {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Ensure proper spacing on mobile */
  .mobile-padding-fix {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Fix grid layouts on mobile */
  .mobile-grid-fix {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Additional mobile fixes */
@media (max-width: 768px) {
  /* Ensure cards don't overflow */
  .card-mobile-fix {
    max-width: calc(100vw - 2rem);
    overflow: hidden;
  }

  /* Fix text overflow */
  .text-mobile-fix {
    word-break: break-word;
    overflow-wrap: break-word;
  }
}

/* Layout alignment fixes */
.sidebar-content-alignment {
  /* Account for sidebar width + border */
  margin-left: calc(16rem + 1px); /* 64 * 0.25rem + 1px border */
}

.sidebar-content-alignment-collapsed {
  /* Account for collapsed sidebar width + border */
  margin-left: calc(4rem + 1px); /* 16 * 0.25rem + 1px border */
}

/* Ensure perfect alignment on large screens */
@media (min-width: 1024px) {
  .sidebar-content-alignment {
    margin-left: 16.0625rem; /* 256px + 1px for border */
    transition: margin-left 300ms ease-in-out;
  }

  .sidebar-content-alignment-collapsed {
    margin-left: 4.0625rem; /* 64px + 1px for border */
    transition: margin-left 300ms ease-in-out;
  }
}

@media (max-width: 1024px) {
  .sidebar-content-alignment,
  .sidebar-content-alignment-collapsed {
    margin-left: 0;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }
}
