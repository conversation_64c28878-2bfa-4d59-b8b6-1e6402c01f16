@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  font-weight: 700;
}

h2 {
  font-size: 1.875rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Loading States */
.loading-spinner {
  animation: spin 1s linear infinite;
  border-radius: 50%;
  height: 1.5rem;
  width: 1.5rem;
  border: 2px solid transparent;
  border-bottom: 2px solid #2563eb;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Design Helpers */
@media (max-width: 640px) {
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-text {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  /* Ensure proper mobile layout */
  body {
    overflow-x: hidden;
  }

  /* Fix mobile navigation issues */
  .mobile-nav-fix {
    position: relative;
    z-index: 60;
  }
}

/* Prevent horizontal scroll on all screen sizes */
html, body {
  overflow-x: hidden;
  width: 100%;
}

/* Fix for mobile viewport issues */
@media (max-width: 1024px) {
  .mobile-content {
    width: 100%;
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Ensure proper spacing on mobile */
  .mobile-padding-fix {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Fix grid layouts on mobile */
  .mobile-grid-fix {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Additional mobile fixes */
@media (max-width: 768px) {
  /* Ensure cards don't overflow */
  .card-mobile-fix {
    max-width: calc(100vw - 2rem);
    overflow: hidden;
  }

  /* Fix text overflow */
  .text-mobile-fix {
    word-break: break-word;
    overflow-wrap: break-word;
  }
}

/* Layout alignment fixes - Precise calculations for perfect alignment */
.sidebar-content-alignment {
  /* Account for sidebar width (256px) + border (1px) = 257px = 16.0625rem */
  margin-left: 16.0625rem;
  transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-content-alignment-collapsed {
  /* Account for collapsed sidebar width (64px) + border (1px) = 65px = 4.0625rem */
  margin-left: 4.0625rem;
  transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure perfect alignment on large screens */
@media (min-width: 1024px) {
  .sidebar-content-alignment {
    margin-left: 16.0625rem; /* 257px total (256px sidebar + 1px border) */
  }

  .sidebar-content-alignment-collapsed {
    margin-left: 4.0625rem; /* 65px total (64px sidebar + 1px border) */
  }
}

/* Mobile responsive - no sidebar offset */
@media (max-width: 1023px) {
  .sidebar-content-alignment,
  .sidebar-content-alignment-collapsed {
    margin-left: 0;
    transition: margin-left 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* Additional layout precision fixes */
@media (min-width: 1024px) {
  /* Ensure sidebar and content are perfectly aligned */
  .sidebar-content-alignment,
  .sidebar-content-alignment-collapsed {
    position: relative;
    /* Prevent any sub-pixel rendering issues */
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Ensure header spans full width correctly */
  .sidebar-content-alignment header,
  .sidebar-content-alignment-collapsed header {
    width: 100%;
    left: 0;
    right: 0;
  }
}

/* Tablet responsive adjustments */
@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar-content-alignment,
  .sidebar-content-alignment-collapsed {
    margin-left: 0;
  }

  /* Ensure proper spacing on tablets */
  .mobile-content {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Small mobile devices */
@media (max-width: 640px) {
  .sidebar-content-alignment,
  .sidebar-content-alignment-collapsed {
    margin-left: 0;
  }

  /* Prevent any layout shifts on small screens */
  body {
    position: relative;
    overflow-x: hidden;
  }
}

/* High DPI and zoom level adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .sidebar-content-alignment,
  .sidebar-content-alignment-collapsed {
    /* Use exact pixel values for high DPI displays */
    transform: translateZ(0);
    will-change: margin-left;
  }
}

/* Browser zoom level compensation */
@media (min-width: 1024px) {
  .sidebar-content-alignment {
    /* Ensure consistent alignment regardless of zoom */
    margin-left: calc(256px + 1px);
  }

  .sidebar-content-alignment-collapsed {
    margin-left: calc(64px + 1px);
  }
}

/* Debug mode for layout alignment (remove in production) */
.debug-layout {
  position: relative;
}

.debug-layout::before {
  content: '';
  position: fixed;
  top: 0;
  left: 256px; /* Sidebar width */
  width: 1px;
  height: 100vh;
  background: red;
  z-index: 9999;
  pointer-events: none;
  opacity: 0.5;
}

.debug-layout.collapsed::before {
  left: 64px; /* Collapsed sidebar width */
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-only {
    display: block !important;
  }

  .debug-layout::before {
    display: none;
  }
}
