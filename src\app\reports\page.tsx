'use client'

import { useEffect, useState } from 'react'
import DashboardLayout from '@/components/DashboardLayout'
import { BarChart3, TrendingUp, TrendingDown, DollarSign } from 'lucide-react'

interface ReportData {
  totalProducts: number
  totalCustomers: number
  totalDebts: number
  totalPaidDebts: number
  totalUnpaidDebts: number
  totalRevenue: number
  topCustomers: Array<{
    name: string
    totalDebt: number
    remainingDebt: number
  }>
  lowStockProducts: Array<{
    name: string
    stock: number
    category: string
  }>
}

interface DebtData {
  totalAmount: number
  isPaid: boolean
}

interface PaymentData {
  amount: number
}

interface CustomerData {
  firstName: string
  lastName: string
  totalDebt: number
  remainingDebt: number
}

interface ProductData {
  name: string
  stock: number
  category: string
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchReportData()
  }, [])

  const fetchReportData = async () => {
    try {
      // Fetch all necessary data
      const [productsRes, customersRes, debtsRes, paymentsRes] = await Promise.all([
        fetch('/api/products'),
        fetch('/api/customers'),
        fetch('/api/debts'),
        fetch('/api/payments'),
      ])

      const [products, customers, debts, payments] = await Promise.all([
        productsRes.json(),
        customersRes.json(),
        debtsRes.json(),
        paymentsRes.json(),
      ])

      // Calculate report metrics
      const totalDebts = debts.reduce((sum: number, debt: DebtData) => sum + debt.totalAmount, 0)
      const totalPaidDebts = debts.filter((debt: DebtData) => debt.isPaid).length
      const totalUnpaidDebts = debts.filter((debt: DebtData) => !debt.isPaid).length
      const totalRevenue = payments.reduce((sum: number, payment: PaymentData) => sum + payment.amount, 0)

      // Top customers by debt
      const topCustomers = customers
        .filter((customer: CustomerData) => customer.remainingDebt > 0)
        .sort((a: CustomerData, b: CustomerData) => b.remainingDebt - a.remainingDebt)
        .slice(0, 5)
        .map((customer: CustomerData) => ({
          name: `${customer.firstName} ${customer.lastName}`,
          totalDebt: customer.totalDebt,
          remainingDebt: customer.remainingDebt,
        }))

      // Low stock products
      const lowStockProducts = products
        .filter((product: ProductData) => product.stock <= 10)
        .sort((a: ProductData, b: ProductData) => a.stock - b.stock)
        .slice(0, 10)
        .map((product: ProductData) => ({
          name: product.name,
          stock: product.stock,
          category: product.category,
        }))

      setReportData({
        totalProducts: products.length,
        totalCustomers: customers.length,
        totalDebts,
        totalPaidDebts,
        totalUnpaidDebts,
        totalRevenue,
        topCustomers,
        lowStockProducts,
      })
    } catch (error) {
      console.error('Error fetching report data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <DashboardLayout title="Reports" subtitle="Business insights and analytics">
        <div className="text-center py-8">
          <div className="text-gray-500">Loading reports...</div>
        </div>
      </DashboardLayout>
    )
  }

  if (!reportData) {
    return (
      <DashboardLayout title="Reports" subtitle="Business insights and analytics">
        <div className="text-center py-8">
          <div className="text-red-500">Failed to load report data</div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout title="Reports" subtitle="Business insights and analytics">
      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-green-600">
                  ₱{reportData.totalRevenue.toLocaleString()}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Outstanding Debts</p>
                <p className="text-2xl font-bold text-red-600">
                  ₱{reportData.totalDebts.toLocaleString()}
                </p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Paid Debts</p>
                <p className="text-2xl font-bold text-blue-600">
                  {reportData.totalPaidDebts}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Unpaid Debts</p>
                <p className="text-2xl font-bold text-orange-600">
                  {reportData.totalUnpaidDebts}
                </p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-500" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Customers by Debt */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Top Customers by Outstanding Debt
            </h3>
            {reportData.topCustomers.length > 0 ? (
              <div className="space-y-3">
                {reportData.topCustomers.map((customer, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{customer.name}</div>
                      <div className="text-sm text-gray-500">
                        Total: ₱{customer.totalDebt.toFixed(2)}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-red-600">
                        ₱{customer.remainingDebt.toFixed(2)}
                      </div>
                      <div className="text-sm text-gray-500">remaining</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">No outstanding debts</p>
            )}
          </div>

          {/* Low Stock Products */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Low Stock Alert (≤10 units)
            </h3>
            {reportData.lowStockProducts.length > 0 ? (
              <div className="space-y-3">
                {reportData.lowStockProducts.map((product, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{product.name}</div>
                      <div className="text-sm text-gray-500">{product.category}</div>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        product.stock === 0 
                          ? 'bg-red-100 text-red-800' 
                          : product.stock <= 5 
                          ? 'bg-orange-100 text-orange-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {product.stock} units
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-4">All products have sufficient stock</p>
            )}
          </div>
        </div>

        {/* Business Summary */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{reportData.totalProducts}</div>
              <div className="text-sm text-gray-600">Total Products</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{reportData.totalCustomers}</div>
              <div className="text-sm text-gray-600">Total Customers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {((reportData.totalPaidDebts / (reportData.totalPaidDebts + reportData.totalUnpaidDebts)) * 100 || 0).toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Payment Rate</div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
