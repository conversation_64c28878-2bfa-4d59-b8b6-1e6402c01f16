'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { paymentSchema, PaymentFormData } from '@/lib/validations'

interface Customer {
  id: string
  firstName: string
  lastName: string
  remainingDebt: number
}

interface CustomerDebt {
  id: string
  productName: string
  totalAmount: number
  isPaid: boolean
}

export default function PaymentForm() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<Customer[]>([])
  const [customerDebts, setCustomerDebts] = useState<CustomerDebt[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<string>('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      customerId: '',
      customerDebtId: '',
      amount: 0,
      notes: '',
    },
  })

  const watchedCustomerId = watch('customerId')

  useEffect(() => {
    fetchCustomers()
  }, [])

  useEffect(() => {
    if (watchedCustomerId) {
      setSelectedCustomer(watchedCustomerId)
      fetchCustomerDebts(watchedCustomerId)
    } else {
      setCustomerDebts([])
    }
  }, [watchedCustomerId])

  const fetchCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      const data = await response.json()
      setCustomers(data)
    } catch (error) {
      console.error('Error fetching customers:', error)
    }
  }

  const fetchCustomerDebts = async (customerId: string) => {
    try {
      const response = await fetch(`/api/debts?customerId=${customerId}&isPaid=false`)
      const data = await response.json()
      setCustomerDebts(data)
    } catch (error) {
      console.error('Error fetching customer debts:', error)
    }
  }

  const onSubmit = async (data: PaymentFormData) => {
    setLoading(true)
    try {
      const response = await fetch('/api/payments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          dateOfPayment: new Date(),
        }),
      })

      if (response.ok) {
        router.push('/payments')
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to record payment')
      }
    } catch (error) {
      console.error('Error recording payment:', error)
      alert('Failed to record payment')
    } finally {
      setLoading(false)
    }
  }

  const selectedCustomerData = customers.find(c => c.id === selectedCustomer)

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Information</h3>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer *
            </label>
            <select
              {...register('customerId')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a customer</option>
              {customers.map((customer) => (
                <option key={customer.id} value={customer.id}>
                  {customer.firstName} {customer.lastName} 
                  {customer.remainingDebt > 0 && ` (Debt: ₱${customer.remainingDebt.toFixed(2)})`}
                </option>
              ))}
            </select>
            {errors.customerId && (
              <p className="mt-1 text-sm text-red-600">{errors.customerId.message}</p>
            )}
          </div>

          {selectedCustomerData && selectedCustomerData.remainingDebt > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                Customer Debt Summary
              </h4>
              <p className="text-sm text-blue-700">
                Total remaining debt: ₱{selectedCustomerData.remainingDebt.toFixed(2)}
              </p>
            </div>
          )}

          {customerDebts.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Specific Debt (Optional)
              </label>
              <select
                {...register('customerDebtId')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">General payment (not for specific debt)</option>
                {customerDebts.map((debt) => (
                  <option key={debt.id} value={debt.id}>
                    {debt.productName} - ₱{debt.totalAmount.toFixed(2)}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Payment Amount (₱) *
            </label>
            <input
              type="number"
              step="0.01"
              {...register('amount', { valueAsNumber: true })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="0.00"
            />
            {errors.amount && (
              <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes (Optional)
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Add any notes about this payment..."
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <button
          type="button"
          onClick={() => router.back()}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? 'Recording...' : 'Record Payment'}
        </button>
      </div>
    </form>
  )
}
