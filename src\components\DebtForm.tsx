'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { customerDebtSchema, CustomerDebtFormData } from '@/lib/validations'

interface Customer {
  id: string
  firstName: string
  lastName: string
}

interface Product {
  id: string
  name: string
  price: number
  stock: number
  netWeight: string
}

export default function DebtForm() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [customers, setCustomers] = useState<Customer[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<CustomerDebtFormData>({
    resolver: zodResolver(customerDebtSchema),
    defaultValues: {
      customerId: '',
      productId: '',
      quantity: 1,
    },
  })

  const watchedProductId = watch('productId')
  const watchedQuantity = watch('quantity')

  useEffect(() => {
    fetchCustomers()
    fetchProducts()
  }, [])

  useEffect(() => {
    if (watchedProductId) {
      const product = products.find(p => p.id === watchedProductId)
      setSelectedProduct(product || null)
    } else {
      setSelectedProduct(null)
    }
  }, [watchedProductId, products])

  const fetchCustomers = async () => {
    try {
      const response = await fetch('/api/customers')
      const data = await response.json()
      setCustomers(data)
    } catch (error) {
      console.error('Error fetching customers:', error)
    }
  }

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/products')
      const data = await response.json()
      setProducts(data.filter((product: Product) => product.stock > 0))
    } catch (error) {
      console.error('Error fetching products:', error)
    }
  }

  const onSubmit = async (data: CustomerDebtFormData) => {
    setLoading(true)
    try {
      const response = await fetch('/api/debts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          dateOfDebt: new Date(),
        }),
      })

      if (response.ok) {
        router.push('/debts')
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to record debt')
      }
    } catch (error) {
      console.error('Error recording debt:', error)
      alert('Failed to record debt')
    } finally {
      setLoading(false)
    }
  }

  const totalAmount = selectedProduct && watchedQuantity 
    ? selectedProduct.price * watchedQuantity 
    : 0

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Debt Information</h3>
        
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer *
            </label>
            <select
              {...register('customerId')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a customer</option>
              {customers.map((customer) => (
                <option key={customer.id} value={customer.id}>
                  {customer.firstName} {customer.lastName}
                </option>
              ))}
            </select>
            {errors.customerId && (
              <p className="mt-1 text-sm text-red-600">{errors.customerId.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Product *
            </label>
            <select
              {...register('productId')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a product</option>
              {products.map((product) => (
                <option key={product.id} value={product.id}>
                  {product.name} ({product.netWeight}) - ₱{product.price.toFixed(2)} 
                  (Stock: {product.stock})
                </option>
              ))}
            </select>
            {errors.productId && (
              <p className="mt-1 text-sm text-red-600">{errors.productId.message}</p>
            )}
          </div>

          {selectedProduct && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">
                Product Details
              </h4>
              <div className="text-sm text-blue-700 space-y-1">
                <p>Name: {selectedProduct.name}</p>
                <p>Net Weight: {selectedProduct.netWeight}</p>
                <p>Price: ₱{selectedProduct.price.toFixed(2)}</p>
                <p>Available Stock: {selectedProduct.stock} units</p>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity *
            </label>
            <input
              type="number"
              min="1"
              max={selectedProduct?.stock || 999}
              {...register('quantity', { valueAsNumber: true })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="1"
            />
            {errors.quantity && (
              <p className="mt-1 text-sm text-red-600">{errors.quantity.message}</p>
            )}
            {selectedProduct && watchedQuantity > selectedProduct.stock && (
              <p className="mt-1 text-sm text-red-600">
                Quantity cannot exceed available stock ({selectedProduct.stock})
              </p>
            )}
          </div>

          {totalAmount > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-green-900 mb-2">
                Debt Summary
              </h4>
              <div className="text-sm text-green-700 space-y-1">
                <p>Unit Price: ₱{selectedProduct?.price.toFixed(2)}</p>
                <p>Quantity: {watchedQuantity}</p>
                <p className="font-medium">Total Amount: ₱{totalAmount.toFixed(2)}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <button
          type="button"
          onClick={() => router.back()}
          className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading || (selectedProduct ? watchedQuantity > selectedProduct.stock : false)}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? 'Recording...' : 'Record Debt'}
        </button>
      </div>
    </form>
  )
}
